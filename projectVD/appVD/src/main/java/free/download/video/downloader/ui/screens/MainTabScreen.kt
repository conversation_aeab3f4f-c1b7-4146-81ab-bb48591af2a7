package free.download.video.downloader.ui.screens

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.TextFieldValue
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.mandi.common.ad.AdmobFactory
import com.tiny.ad.network.AdCachePool
import com.tiny.domain.ext.logEvent
import com.tiny.lib.web.view.sniffer.TabManager
import com.tinypretty.component.GlobalModule
import com.tinypretty.ui.componets.ComposableWrap
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.theme.MT
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.ui.components.UpdateDialog
import free.download.video.downloader.ui.screens.downloader.DownloadedScreen
import free.download.video.downloader.ui.screens.home.HomePageScreen
import free.download.video.downloader.ui.screens.music.MusicScreen
import free.download.video.downloader.ui.screens.player.PlayerScreen
import free.download.video.downloader.ui.screens.tab.TabNewScreen
import free.download.video.downloader.ui.screens.tab.TabSelectScreen
import free.download.video.downloader.ui.screens.web.AppBottomActionBar
import free.download.video.downloader.ui.screens.web.ParseResultHint
import free.download.video.downloader.ui.screens.web.ParseResultScreen
import free.download.video.downloader.ui.screens.web.SnifferWebView
import free.download.video.downloader.ui.screens.web.WebNavigateBar
import free.download.video.downloader.viewmodel.app.AppPage
import free.download.video.downloader.viewmodel.app.VideoPage

/**
 * <AUTHOR>
 * @Since 2024/03/15
 */

@Composable
fun MainTabScreen() {
    UpdateDialog()
    val log = GlobalModule.newLog("App")
    log.i { "App redraw" }
    val appVM = LocalAppViewModel.current
    val appPage = appVM.appPage.collectAsState()

    val stateShowTabSelect = remember { mutableStateOf(false) }
    val stateLongPressUrl = remember { mutableStateOf("") }
    val webNavigateBarVisible = remember { mutableStateOf(true) }
    val editUrl = remember { mutableStateOf(TextFieldValue("")) }
    val snifferResultCount = rememberSaveable { mutableIntStateOf(0) }

    ConstraintLayout(
        modifier = Modifier
            .background(MT.color.background)
            .fillMaxSize()
    ) {
        val (refParseResult, refParseResultHint, refWeb, refApp, refNavBottom, refNavTop) = createRefs()

        Column(Modifier.constrainAs(refWeb) {
            top.linkTo(refNavTop.bottom)
            bottom.linkTo(refNavBottom.top)
            height = Dimension.fillToConstraints
        }) {
            WebScreen(
                Modifier
                    .fillMaxSize(), appPage, editUrl = editUrl, stateLongPressUrl = stateLongPressUrl, scrolledUp = webNavigateBarVisible
            )
            50.SpacerFix()
        }


        AppScreen(Modifier.constrainAs(refApp) {
            height = Dimension.fillToConstraints
            top.linkTo(refNavTop.bottom)
            bottom.linkTo(refNavBottom.top)
        }, appPage)

        WebNavigateBar(
            Modifier.constrainAs(refNavTop) {
                top.linkTo(parent.top)
            },
            editUrl, appPage,
            webNavigateBarVisible
        )

        ParseResultHint(modifier = Modifier.constrainAs(refParseResultHint) {
            bottom.linkTo(refNavBottom.top)
        }, appPageState = appPage, scrollUp = webNavigateBarVisible, snifferResultCount = snifferResultCount)

        ParseResultScreen(
            Modifier
                .wrapContentHeight()
                .constrainAs(refParseResult) {
                    bottom.linkTo(refNavBottom.top)
                }, appPage, webNavigateBarVisible, snifferResultCount
        )

        AppBottomActionBar(Modifier.constrainAs(refNavBottom) {
            bottom.linkTo(parent.bottom)
        }, editUrl, showTabSelected = stateShowTabSelect)
    }

    ComposableWrap {
        val tabState = TabManager.mTabInfo.collectAsState().value
        BackHandler {
            val canNotBack = tabState == null || !tabState.snifferDelegate.canBack()

            if (canNotBack) {
                editUrl.value = TextFieldValue("")
                appVM.toHomePage(appVM.appPage.value is AppPage.Home)
            } else {
                appVM.intoBrowser {
                    tabState?.snifferDelegate?.goBack()
                }
            }
        }
    }


    TabSelectScreen(mutableState = stateShowTabSelect)
    TabNewScreen(longPressUrl = stateLongPressUrl)
}

@Composable
private fun WebScreen(modifier: Modifier, appPage: State<AppPage>, editUrl: MutableState<TextFieldValue>, stateLongPressUrl: MutableState<String>, scrolledUp: MutableState<Boolean>? = null) {
    val log = GlobalModule.newLog("WebScreen")
    val browserPage = appPage.value

    if (browserPage is AppPage.Browser) {
        val tabInfo = TabManager.mTabInfo.collectAsState().value
        val appVM = LocalAppViewModel.current
        if (tabInfo == null) {
            // todo by shawn no important 检查为什么播放返回后，界面为空
            log.i { "WebScreen tabInfo == null return" }
            return
        }

        if (browserPage.changeTab) {
            appVM.toBrowserPage(tabInfo, null, browserPage.requestFocus, false)
            log.i { "WebScreen change tab start" }
            return
        }
        log.i { "WebScreen change show tab start" }
        val snifferDelegate = tabInfo.snifferDelegate
        editUrl.value = TextFieldValue(snifferDelegate.url())
        snifferDelegate.jsInterface.onLongPressUrl = {
            stateLongPressUrl.value = it
        }
        SnifferWebView(
            modifier, snifferDelegate, scrolledUp
        )
    }
}


@Composable
private fun AppScreen(modifier: Modifier, appPage: State<AppPage?>) {
    Box(modifier = modifier.fillMaxSize()) {
        val activity = activity() ?: return
        AdCachePool.loadAdAsync(activity, AdmobFactory.nativeAdGroup())
        AdCachePool.loadAdAsync(activity, AdmobFactory.popAdGroup())
        when (appPage.value) {
            is AppPage.Home -> {
                "AppScreen".logEvent("page" to "HomePageScreen")
                HomePageScreen()
            }

            is AppPage.Download -> {
                "AppScreen".logEvent("page" to "DownloadedScreen")
                DownloadedScreen()
            }

            is AppPage.MUSIC -> {
                "AppScreen".logEvent("page" to "MUSIC")
                MusicScreen()
            }

            else -> {}
        }
    }
}

@Composable
fun VideoScreen() {
    val appVM = LocalAppViewModel.current
    val vp = appVM.videoPage.collectAsState()
    vp.value?.let {
        when (it) {
            is VideoPage.PlayLocal -> {
                PlayerScreen(file = it.file)
            }

            is VideoPage.PlayUrl -> {
                PlayerScreen(file = it.file)
            }
        }
    }
}

