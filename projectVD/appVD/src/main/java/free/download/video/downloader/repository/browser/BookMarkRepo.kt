package free.download.video.downloader.repository.browser

import CoroutineTask
import androidx.room.Room
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.tinypretty.component.GlobalModule.mApp
import com.tinypretty.component.GlobalModule.mL
import free.download.video.downloader.model.bookmark.BookMarkEntity
import free.download.video.downloader.model.bookmark.BookmarkDatabase
import free.download.video.downloader.model.bookmark.BookmarkDatabaseOld
import free.download.video.downloader.repository.cache.CachedValue
import free.download.video.downloader.utils.DatabaseManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Since 2024/01/06
 */
object BookMarkRepo {
    private val TAG = "BookMarkRepo"

    val MIGRATION_2_3: Migration = object : Migration(2, 3) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // Write the SQL query to alter the database schema
            // For example, if you added a new column to the BookMarkEntity table:
            // Add 'icon' column to 'bookmark' table
            database.execSQL("ALTER TABLE bookmark ADD COLUMN icon TEXT DEFAULT '' NOT NULL")
            // Add 'exifJson' column to 'bookmark' table
            database.execSQL("ALTER TABLE bookmark ADD COLUMN exifJson TEXT DEFAULT '' NOT NULL")
        }
    }

    val bookMark by lazy {
        Room.databaseBuilder(
            mApp, BookmarkDatabase::class.java,
            "database_bookmark"
        ).addMigrations(MIGRATION_2_3)
            .build().bookMarkDao().apply {
            upgrade()
        }
    }

    /**
     * 安全地插入书签记录，会先检查数据库记录数量
     * @param bookMark 要插入的书签实体
     * @return 插入的记录ID
     */
    fun safeInsert(bookMark: BookMarkEntity) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 检查数据库记录数量
                DatabaseManager.checkBeforeInsert(<EMAIL>)
                // 插入记录
                val id = <EMAIL>(bookMark)
                mL.d { "$TAG -> Inserted bookmark with ID: $id, title: ${bookMark.title}" }
            } catch (e: Exception) {
                mL.e { "$TAG -> Error inserting bookmark: ${e.message}" }
            }
        }
    }

    private fun upgrade() {
        if (!CachedValue.bookMarkUpgraded){
            CachedValue.bookMarkUpgraded = true
            CoroutineTask("loadOldBookMark").io().launch {
                BookmarkDatabaseOld(mApp).getAllBookmarksSorted().forEach {
                    bookMark.insert(BookMarkEntity().apply {
                        title = it.first
                        url = it.second
                    })
                }
                bookMark.getAll().collect { list ->
                    if (list.isEmpty()) {
                        // The list is empty
                        bookMark.insert(BookMarkEntity().apply {
                            title = "Twitter"
                            url = "https://twitter.com/"
                            icon = "https://twitter.com/favicon.ico"
                        })
                        bookMark.insert(BookMarkEntity().apply {
                            title = "Instagram"
                            url = "https://www.instagram.com/"
                            icon = "https://www.instagram.com/favicon.ico"
                        })

                        bookMark.insert(BookMarkEntity().apply {
                            title = "FB"
                            url = "https://m.facebook.com/"
                            icon = "https://www.facebook.com/favicon.ico"
                        })

                        CoroutineTask.cancelTask("Data inserted, cancelling coroutine")
                    }
                }
            }
        }
    }
}